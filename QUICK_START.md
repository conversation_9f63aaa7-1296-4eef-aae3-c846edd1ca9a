# 🚀 Quick Start Guide

## Option 1: Automated Setup (Recommended)

1. **Run the setup script:**
   ```bash
   python setup.py
   ```

2. **Add your OpenAI API key:**
   - Edit the `.env` file
   - Replace `your-openai-api-key-here` with your actual API key

3. **Start the application:**
   ```bash
   streamlit run sap9.py
   ```
   Or double-click `run_app.bat` on Windows

## Option 2: Manual Setup

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Create environment file:**
   ```bash
   copy .env.example .env    # Windows
   cp .env.example .env      # Linux/Mac
   ```

3. **Add your OpenAI API key to .env:**
   ```
   OPENAI_API_KEY=your-actual-api-key-here
   ```

4. **Test the setup:**
   ```bash
   python test_setup.py
   ```

5. **Start the application:**
   ```bash
   streamlit run sap9.py
   ```

## Testing the Tool

1. **Open your browser** to `http://localhost:8501`
2. **Upload the sample invoice** (`sample_invoice.pdf`) or your own PDF
3. **Click "🚀 Run Full Extraction for All Files"**
4. **Review the results** and generated regex patterns

## What You'll Get

- ✅ Extracted text from PDF
- ✅ AI-identified invoice fields
- ✅ Generated regex patterns
- ✅ Pattern verification results
- ✅ Direct links to test patterns on Regex101

## Troubleshooting

- **"OpenAI API key not found"**: Make sure you've added your API key to the `.env` file
- **Import errors**: Run `pip install -r requirements.txt`
- **PDF processing issues**: Ensure your PDFs contain text (not just images)

## Need Help?

Check the full `README.md` for detailed documentation and troubleshooting tips.
