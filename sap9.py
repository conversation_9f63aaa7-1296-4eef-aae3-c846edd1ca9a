import openai
import streamlit as st
import os
import re
import urllib.parse
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set OpenAI API key
openai.api_key = os.getenv('OPENAI_API_KEY')

if not openai.api_key:
    st.error("⚠️ OpenAI API key not found! Please set OPENAI_API_KEY in your .env file.")
    st.stop()


# Set browser tab title and icon
st.set_page_config(page_title="Regex Generator", page_icon="🧪")

st.title("Regex Generator Tool")

# === PDF Text and Table Extractor ===
import fitz  # PyMuPDF
import re
import os
import tempfile

def text_extractor(pdf_file, output_filename="invoice_full_extraction.txt"):
    """
    Extracts text from a PDF using PyMuPDF, replaces multiple spaces with tabs,
    and saves the result to a text file.
    """
    full_content = ""
    try:
        # Save the uploaded PDF to a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
            tmp_file.write(pdf_file.read())
            tmp_file_path = tmp_file.name

        # Open the PDF and extract text with layout preservation
        doc = fitz.open(tmp_file_path)
        for page_number in range(doc.page_count):
            page = doc.load_page(page_number)
            page_text = page.get_text("text", sort=True)

            # Replace multiple spaces with tabs
            processed_page_text = re.sub(r' {2,}', '\t', page_text)

            full_content += f"\n--- Page {page_number + 1} ---\n{processed_page_text}\n"
        doc.close()

        # Save to output file
        with open(output_filename, "w", encoding="utf-8") as f:
            f.write(full_content)

        print(f"\n✅ All text saved to: {output_filename}")
        return output_filename

    finally:
        # Clean up the temp file
        if tmp_file_path and os.path.exists(tmp_file_path):
            os.unlink(tmp_file_path)


def api_call_for_data_extraction_from_txt(extracted_text, output_filename="invoice_extracted_fields.txt"):

    # === Fields you want to extract ===
    target_fields = [
        "invoiceNumber",
        "poNumber",
        "date",
        "subTotal",
        "tax",
        "documentTotal",
        "deliveryFee",
        "saleline"
    ]

    # === Field-specific prompt instructions ===
    def build_prompt(field, pdf_text):
        field_instructions = {
            "invoiceNumber": """Extract the Invoice Number from the following text.
    It might be labeled as: 'Invoice Number', 'Credit Note Number', 'Credit Number', 'Document Number'.
    Return only the number. If missing, return 'Not Found'.""",

            "poNumber": """Extract the Purchase Order Number from the following text.
    It might be labeled as: 'PO Number', 'Job Number', 'Customer Order Number'.
    Return only the number. If missing, return 'Not Found'.""",

            "date": """Extract the Invoice Date from the following text.
    Look for labels like: 'Invoice Date', 'Credit Date', or 'Date' (ignore 'Purchase Date').
    Return the exact date text as written. If missing, return 'Not Found'.""",

            "subTotal": """Extract the Subtotal amount (before tax and fees) from the following text.
    Label might be: 'Sub Total', 'Subtotal'. Return only the number.""",

            "tax": """Extract the total Tax or GST amount from the invoice text.
    Label could be: 'GST', 'TAX'. Return only the numeric value.""",

            "documentTotal": """Extract the Document Total (final invoice amount including tax and all fees).
    Label might be: 'TOTAL', 'Total Amount'. Return only the number.""",

            "deliveryFee": """Extract the Delivery Fee (shipping cost) from the text.
    Label might be: 'Freight Charge', 'Delivery Fee'.
    If not present, return '0'.""",

            "saleline": """Extract the purchased item table from the invoice text.
    For each line, return:
    - productId
    - description
    - qty
    - unitPrice
    - discount (if present)
    - tax (if present)
    - totalPrice
    - extendedDescription (if the description continues to the next line).

    Return as a JSON array.
    If no items are found, return an empty list []."""
        }

        return [
            {"role": "system", "content": f"You are an invoice extraction assistant. Focus only on extracting the field: {field}."},
            {"role": "user", "content": f"""
    {field_instructions[field]}

    Text:
    ---
    {pdf_text}
    ---
    Return only the value for {field}, in JSON format.
    """}
        ]

    # === Extraction loop + saving results to TXT ===
    def extract_invoice_fields_and_save(pdf_text, output_file):
        extracted_data = {}

        with open(output_file, "w", encoding="utf-8") as f:
            for field in target_fields:
                messages = build_prompt(field, pdf_text)

                response = openai.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=messages,
                    temperature=0.0
                )

                result = response.choices[0].message.content.strip()
                extracted_data[field] = result

                # Write to txt file
                f.write(f"{field}:\n{result}\n\n")

                print(f"\n✅ Extracted [{field}]:\n{result}\n")

        print(f"\n💾 All extracted fields saved to: {output_file}")
        return extracted_data

    # Run the extraction and save the output
    final_results = extract_invoice_fields_and_save(extracted_text, output_filename)

    print("\n🎯 Final Structured Output:\n", final_results)
    return final_results

def json_to_plaintext(field_path="invoice_extracted_fields.txt", output_path="invoice_extracted_plaintext.txt"):
    with open(field_path, "r", encoding="utf-8") as f:
        extracted_json = f.read()

    # === Build the instruction to reformat JSON into plain text ===
    messages = [
        {"role": "system", "content": "You are a formatting assistant for invoice data. Your job is to convert JSON into a clean human-readable plain text list."},
        {"role": "user", "content": f"""
    Please reformat the following JSON invoice data into plain text.

    Input JSON:
    ---
    {extracted_json}
    ---

    Output Format Example:
    invoiceNumber: XXXXXXXX
    poNumber: XXXXXXXX
    date: XX/XX/XXXX
    subTotal: XXX.XX
    tax: XX.XX
    documentTotal: XXX.XX
    deliveryFee: XX.XX


    If the JSON has saleline arrays, list each item on a new line in the format:
    saleline:
    - productId: XXX, description: ..., qty: X.X, unitPrice: X.XX, totalPrice: X.XX

    👉 Ensure all date values are in the format DD/MM/YYYY, even if they were originally written differently.
    """}
    ]

    # === Call OpenAI API to convert JSON to plain text ===
    response = openai.chat.completions.create(
        model="gpt-4o-mini",
        messages=messages,
        temperature=0.0
    )

    plain_text_result = response.choices[0].message.content.strip()

    # === Save the plain text to a file ===
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(plain_text_result)

    print("\n✅ JSON converted to plain text and saved to: invoice_extracted_plaintext.txt")
    print("\n📋 Cleaned Plain Text:\n")
    print(plain_text_result)
    return plain_text_result


def find_original_snippets_with_layout(reference_path, original_text_path, output_path):
    with open(reference_path, "r", encoding="utf-8") as f:
        reference_text = f.read()

    with open(original_text_path, "r", encoding="utf-8") as f:
        original_text = f.read()

    messages = [
        {"role": "system", "content": "You're an assistant that extracts relevant parts from original documents based on field references."},
        {"role": "user", "content": f"""
You are given structured invoice fields and their values (extracted from plain text) and a raw OCR-like original document text.

---REFERENCE DATA (Structured Fields & Values)---
{reference_text}
--------------------------------------------------

Your task is to locate the most relevant **snippets** from the original text that correspond to the values in the reference data. The original text may use **different field names** (e.g., "Customer Order Number" instead of "PO Number"), so use the values and surrounding context as anchors.

---ORIGINAL TEXT---
{original_text}
--------------------

🛠️ INSTRUCTIONS:
- Extract only the **lines or blocks of text from the original text** that clearly include the same values as in the reference data.
- Prioritize semantic matches and context (e.g., amounts, numbers, dates).
- Do **not** attempt to reformat or reorder anything.
- Retain **exact spacing, line breaks, and punctuation**.
- Avoid duplication. If multiple lines mention the same value, pick the one with the most relevant surrounding context.
- Output should be a **subset of the original text**, only including the lines necessary to back up the extracted fields.

🎯 GOAL: Precisely map the values from the reference data back to their real locations in the original text, even if the labels differ.

Now, extract the relevant snippets from the original text that best match the reference data.
"""}

    ]

    response = openai.chat.completions.create(
        model="gpt-4o-mini",
        messages=messages,
        temperature=0
    )

    layout_preserved_snippets = response.choices[0].message.content.strip()

    with open(output_path, "w", encoding="utf-8") as f:
        f.write(layout_preserved_snippets)

    print(f"\n✅ Extracted relevant original snippets saved to: {output_path}")
    return layout_preserved_snippets


def Regex_generator(input_path="invoice_original_text.txt",
    reference_field_path="invoice_extracted_plaintext.txt",
    output_path="final_invoice_regex_patterns.txt"):
    import openai

    # === Load invoice text ===

        # === Load original invoice plain text ===
    with open(input_path, "r", encoding="utf-8") as f:
        original_invoice_text = f.read()

    # === Load AI-refined field-level plain text (from JSON) ===
    with open(reference_field_path, "r", encoding="utf-8") as f:
        reference_plaintext = f.read()

    # === Build highly precise prompt ===
    messages = [
        {
            "role": "system",
            "content": (
                "You are a professional regex engine designed for accurate extraction of structured invoice data. "
                "Only respond with .NET / PCRE-compatible regex patterns using named groups like (?<fieldName>...)."
            )
        },
        {
            "role": "user",
            "content": f"""
You are given:
1️⃣ The **original invoice text** (shows layout and structure), and
2️⃣ A **cleaned, AI-extracted reference** of field values (shows labels and expected formats).

--- ORIGINAL INVOICE TEXT ---
{original_invoice_text}
-----------------------------

--- FIELD REFERENCE TEXT (from AI-extracted JSON) ---
{reference_plaintext}
------------------------------------------------------

🎯 Your job:
- Use the field reference text to understand the exact data to extract.
- Use the original invoice text to **anchor** each regex based on label position, tab alignment, or nearby words.

🛡️ REGEX RULES:
- Use .NET / PCRE syntax only.
- Use **named groups only** like (?<fieldName>...).
- Match only the **correct value** using strict anchors from the original invoice.
- Do **not** use loose/generic patterns. Do not include explanations.
- Always assume values appear exactly as shown in the reference text.
- Use `^`, `:`, tabs/spaces, etc., to narrow context and avoid false matches.
- If field labels have variants (e.g., "PO Number" vs "P.O. No"), use alternations.

📌 For `saleline`:
- Match a **full line** with these groups: `productId`, `description`, `qty`, `unitPrice`, `totalPrice`.
- Assume whitespace or tab-delimited alignment.

📅 For date field, use:
\\s*(?<day>\\d{{2}})/(?<month>\\d{{2}})/(?<year>\\d{{4}})\\s*

🧾 Output Format (no code blocks or markdown):
invoiceNumber_regex: [your regex]
poNumber_regex: [your regex]
date_regex: [your regex]
subTotal_regex: [your regex]
tax_regex: [your regex]
documentTotal_regex: [your regex]
deliveryFee_regex: [your regex]
saleline_regex: [your regex]
"""
        }
    ]

    # === Make the API call ===
    response = openai.chat.completions.create(
        model="gpt-4o",
        messages=messages,
        temperature=0.0
    )

    # === Extract response and save to file ===
    final_regex_output = response.choices[0].message.content.strip()

    with open(output_path, "w", encoding="utf-8") as f:
        f.write(final_regex_output)

    print("\n✅ Final regex patterns saved to:", output_path)
    print("\n🎯 Generated Regex Patterns:\n")
    print(final_regex_output)

    return final_regex_output


def verify_and_fix_regex_patterns_via_gpt(invoice_path="invoice_extracted_plaintext.txt", regex_path="final_invoice_regex_patterns.txt"):
    with open(invoice_path, "r", encoding="utf-8") as f:
        invoice_text = f.read()

    with open(regex_path, "r", encoding="utf-8") as f:
        raw_patterns = f.read()

    verification_prompt = f"""
You are an expert in validating and correcting regex patterns used for extracting invoice data. Below is the original invoice text and the regex patterns that were generated.

--- INVOICE TEXT START ---
{invoice_text}
--- INVOICE TEXT END ---

--- REGEX PATTERNS START ---
{raw_patterns}
--- REGEX PATTERNS END ---

✅ Your job:
- Test each pattern logically against the text.
- If the pattern is likely to fail or be too loose/strict, **fix it**.
- Use .NET/PCRE syntax (named groups: (?<name>...)).
- Escape special characters correctly.
- Maintain the format exactly as shown below.

💡 Output Format (strictly use this format, overwrite incorrect ones only):

invoiceNumber_regex: [your fixed regex]
poNumber_regex: [your fixed regex]
date_regex: [your fixed regex]
subTotal_regex: [your fixed regex]
tax_regex: [your fixed regex]
documentTotal_regex: [your fixed regex]
deliveryFee_regex: [your fixed regex]
saleline_regex: [your fixed regex]
"""

    response = openai.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {"role": "system", "content": "You are a skilled regex verification assistant specialized in invoice extraction tasks."},
            {"role": "user", "content": verification_prompt}
        ],
        temperature=0.0
    )

    verified_patterns = response.choices[0].message.content.strip()

    # Generate new filename with _refined suffix
    base, ext = os.path.splitext(regex_path)
    refined_path = f"{base}_refined{ext}"

    # Save to new file
    with open(refined_path, "w", encoding="utf-8") as f:
        f.write(verified_patterns)

    print("\n🔧 Regex patterns verified and updated by GPT.")
    print(verified_patterns)
    return verified_patterns


def regex_verification(invoice_path="invoice_extracted_plaintext.txt", regex_path="final_invoice_regex_patterns.txt"):
    result_summary = ""
    with open(invoice_path, "r", encoding="utf-8") as f:
        invoice_text = f.read()

    # === Load the regex patterns ===
    regex_patterns = {}
    with open(regex_path, "r", encoding="utf-8") as f:
        for line in f:
            if "_regex:" in line:
                parts = line.split(":", 1)
                field = parts[0].strip()
                pattern = parts[1].strip()

                # Replace .NET style named groups with Python compatible syntax
                pattern = re.sub(r'\(\?<(\w+)>', r'(?P<\1>', pattern)

                # Remove any trailing markdown ticks or extra whitespace
                pattern = pattern.strip("` ")

                regex_patterns[field] = pattern

    # === Apply regex patterns and print results ===
    print("\n🔍 === Regex Verification Results ===\n")

    for field, pattern in regex_patterns.items():
        try:
            compiled = re.compile(pattern, re.MULTILINE)  # multiline: so ^ and $ work on lines
            matches = list(compiled.finditer(invoice_text))

            if matches:
                for idx, match in enumerate(matches, start=1):
                    if match.groupdict():
                        print(f"✅ {field} [Match {idx}]: {match.groupdict()}")
                        result_summary += f"✅ {field} [Match {idx}]: {match.groupdict()}\n"
                    else:
                        print(f"✅ {field} [Match {idx}]: {match.group(0)}")
                        result_summary += f"✅ {field} [Match {idx}]: {match.group(0)}\n"
                print()  # extra line
                result_summary += "\n"
            else:
                print(f"❌ {field}: No match found.\n")
                result_summary += f"❌ {field}: No match found.\n\n"

        except re.error as e:
            print(f"⚠️ {field}: Invalid regex pattern -> {e}\n")
            result_summary += f"⚠️ {field}: Invalid regex pattern -> {e}\n\n"

    print("🎯 Verification complete.")
    result_summary += "🎯 Verification complete.\n"

    return result_summary

# === Streamlit File Uploader ===

# === Upload File ===


import streamlit as st
import urllib.parse

# === Upload File ===
uploaded_files = st.file_uploader("Upload one or more PDFs", type=["pdf"], accept_multiple_files=True)

# Global button to process all PDFs
if uploaded_files:
    if st.button("🚀 Run Full Extraction for All Files"):
        st.session_state['run_all'] = True

if uploaded_files and st.session_state.get('run_all'):
    for idx, pdf_file in enumerate(uploaded_files, start=1):
        file_name = pdf_file.name
        st.markdown(f"## 📄 Results for: `{file_name}`")

        with st.spinner(f"Processing {file_name}..."):
            # === STEP 1: Text Extraction ===
            pdf_file.seek(0)
            text_output_path = f"{file_name}_full_extraction.txt"
            output_path = text_extractor(pdf_file, output_filename=text_output_path)

            with open(output_path, "r", encoding="utf-8") as f:
                extracted_text = f.read()
            st.text_area(f"📝 Extracted Text from {file_name}", extracted_text, height=300)

            # === STEP 2: GPT Field Extraction ===
            field_output_path = f"{file_name}_extracted_fields.txt"
            final_results = api_call_for_data_extraction_from_txt(extracted_text, field_output_path)

            with open(field_output_path, "r", encoding="utf-8") as f:
                extracted_fields = f.read()
            st.text_area(f"📊 Extracted Fields from {file_name}", extracted_fields, height=300)

            # === STEP 3: JSON to Plain Text ===
            plain_text_path = f"{file_name}_plaintext.txt"
            formatted = json_to_plaintext(field_output_path, plain_text_path)

            with open(plain_text_path, "r", encoding="utf-8") as f:
                plain_text_content = f.read()
            st.text_area(f"🧾 Formatted Output for {file_name}", formatted, height=300)

            # === STEP 4: Match Fields in Original Text ===
            layout_snippet_path = f"{file_name}_layout_preserved.txt"
            layout_preserved = find_original_snippets_with_layout(
                plain_text_path, text_output_path, layout_snippet_path
            )

            with open(layout_snippet_path, "r", encoding="utf-8") as f:
                layout_preserved_text = f.read()
            st.text_area(f"🧷 Relevant Original Snippets from {file_name}", layout_preserved_text, height=300)

            # === STEP 5: Generate and Verify Regex ===
            regex_output_path = f"{file_name}_regex_patterns.txt"
            field_reference_path = f"{file_name}_plaintext.txt"
            _ = Regex_generator(layout_snippet_path, field_reference_path, regex_output_path)

            fixed_patterns = verify_and_fix_regex_patterns_via_gpt(
                invoice_path=layout_snippet_path,
                regex_path=regex_output_path
            )

            st.markdown(f"### 🛠️ Fixed Regex Patterns for `{file_name}`")
            pattern_lines = fixed_patterns.splitlines()
            for line in pattern_lines:
                if "_regex:" in line:
                    try:
                        field_key, regex = line.split(":", 1)
                        field_key = field_key.strip().replace("_regex", "")
                        regex = regex.strip()
                        field_name = field_key.replace("invoiceNumber", "Invoice Number") \
                                              .replace("poNumber", "PO Number") \
                                              .replace("date", "Date") \
                                              .replace("subTotal", "Subtotal") \
                                              .replace("tax", "Tax") \
                                              .replace("documentTotal", "Document Total") \
                                              .replace("deliveryFee", "Delivery Fee") \
                                              .replace("saleline", "Saleline")

                        st.markdown(f"**🔹 {field_name}**")
                        st.code(regex, language="regex")
                        encoded_regex = urllib.parse.quote(regex)
                        encoded_text = urllib.parse.quote(plain_text_content)
                        regex101_url = f"https://regex101.com/?regex={encoded_regex}&testString={encoded_text}&flavor=pcre"
                        st.markdown(f"[🔎 Test on Regex101](<{regex101_url}>)", unsafe_allow_html=True)
                    except ValueError:
                        continue

            result = regex_verification(layout_snippet_path, regex_output_path)
            st.text_area(f"🧪 Regex Verification for {file_name}", result, height=300)

        st.divider()
