```plaintext
invoiceNumber_regex: ^\s*Invoice Number:\s*(?<invoiceNumber>INV-\d{4}-\d{3})\s*$
poNumber_regex: ^\s*PO Number:\s*(?<poNumber>PO-\d{6})\s*$
date_regex: ^\s*Invoice Date:\s*(?<date>\d{2}/\d{2}/\d{4})\s*$
subTotal_regex: ^\s*Sub Total:\s*\$(?<subTotal>\d{1,3}(?:,\d{3})*\.\d{2})\s*$
tax_regex: ^\s*GST \(10%\):\s*\$(?<tax>\d{1,3}(?:,\d{3})*\.\d{2})\s*$
documentTotal_regex: ^\s*TOTAL:\s*\$(?<documentTotal>\d{1,3}(?:,\d{3})*\.\d{2})\s*$
deliveryFee_regex: ^\s*Delivery Fee:\s*\$(?<deliveryFee>\d{1,3}(?:,\d{3})*\.\d{2})\s*$
saleline_regex: ^\s*(?<productId>PROD-\d{3})\s+(?<description>[\w\s-]+)\s+(?<qty>\d+\.\d+)\s+\$(?<unitPrice>\d{1,3}(?:,\d{3})*\.\d{2})\s+\$(?<totalPrice>\d{1,3}(?:,\d{3})*\.\d{2})\s*$
```