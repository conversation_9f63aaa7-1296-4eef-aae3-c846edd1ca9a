#!/usr/bin/env python3
"""
Create a sample PDF invoice for testing the regex generator tool
"""

from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors

def create_sample_invoice():
    """Create a sample invoice PDF for testing"""
    
    # Create the PDF
    doc = SimpleDocTemplate("sample_invoice.pdf", pagesize=letter)
    styles = getSampleStyleSheet()
    story = []
    
    # Title
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=1  # Center alignment
    )
    story.append(Paragraph("INVOICE", title_style))
    
    # Company info
    company_style = ParagraphStyle(
        'Company',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=20
    )
    story.append(Paragraph("ABC Company Ltd<br/>123 Business Street<br/>City, State 12345<br/>Phone: (*************", company_style))
    
    # Invoice details
    invoice_data = [
        ['Invoice Number:', 'INV-2024-001'],
        ['PO Number:', 'PO-789456'],
        ['Invoice Date:', '15/03/2024'],
        ['Customer Order Number:', 'ORD-123456']
    ]
    
    invoice_table = Table(invoice_data, colWidths=[2*inch, 2*inch])
    invoice_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
    ]))
    story.append(invoice_table)
    story.append(Spacer(1, 20))
    
    # Bill to
    story.append(Paragraph("Bill To:", styles['Heading2']))
    story.append(Paragraph("XYZ Corporation<br/>456 Client Avenue<br/>Client City, State 67890", styles['Normal']))
    story.append(Spacer(1, 20))
    
    # Items table
    items_data = [
        ['Product ID', 'Description', 'Qty', 'Unit Price', 'Total Price'],
        ['PROD-001', 'Professional Services - Consulting', '10.0', '$150.00', '$1,500.00'],
        ['PROD-002', 'Software License - Annual', '1.0', '$500.00', '$500.00'],
        ['PROD-003', 'Training Materials', '5.0', '$50.00', '$250.00']
    ]
    
    items_table = Table(items_data, colWidths=[1*inch, 2.5*inch, 0.8*inch, 1*inch, 1*inch])
    items_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('ALIGN', (1, 1), (1, -1), 'LEFT'),  # Description left-aligned
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    story.append(items_table)
    story.append(Spacer(1, 20))
    
    # Totals
    totals_data = [
        ['Sub Total:', '$2,250.00'],
        ['Delivery Fee:', '$25.00'],
        ['GST (10%):', '$227.50'],
        ['TOTAL:', '$2,502.50']
    ]
    
    totals_table = Table(totals_data, colWidths=[2*inch, 1.5*inch])
    totals_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
        ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('LINEABOVE', (0, -1), (-1, -1), 2, colors.black),
    ]))
    story.append(totals_table)
    
    # Build PDF
    doc.build(story)
    print("✅ Sample invoice created: sample_invoice.pdf")

if __name__ == "__main__":
    try:
        create_sample_invoice()
    except ImportError:
        print("❌ ReportLab not installed. Installing...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "reportlab"])
        create_sample_invoice()
