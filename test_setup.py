#!/usr/bin/env python3
"""
Test script to verify the setup of the PDF Invoice Regex Generator Tool
"""

import sys
import os

def test_imports():
    """Test if all required packages can be imported"""
    print("🔍 Testing imports...")
    
    try:
        import streamlit
        print("✅ Streamlit imported successfully")
    except ImportError as e:
        print(f"❌ Streamlit import failed: {e}")
        return False
    
    try:
        import openai
        print("✅ OpenAI imported successfully")
    except ImportError as e:
        print(f"❌ OpenAI import failed: {e}")
        return False
    
    try:
        import fitz  # PyMuPDF
        print("✅ PyMuPDF imported successfully")
    except ImportError as e:
        print(f"❌ PyMuPDF import failed: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv imported successfully")
    except ImportError as e:
        print(f"❌ python-dotenv import failed: {e}")
        return False
    
    return True

def test_env_file():
    """Test if .env file exists and has API key"""
    print("\n🔍 Testing environment setup...")
    
    if not os.path.exists('.env'):
        print("❌ .env file not found. Please copy .env.example to .env and add your API key.")
        return False
    
    print("✅ .env file found")
    
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key or api_key == 'your-openai-api-key-here':
        print("❌ OpenAI API key not set or still using placeholder. Please update your .env file.")
        return False
    
    print("✅ OpenAI API key is set")
    return True

def test_streamlit():
    """Test if streamlit can run"""
    print("\n🔍 Testing Streamlit...")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, '-m', 'streamlit', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Streamlit version: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Streamlit test failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Streamlit test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 PDF Invoice Regex Generator - Setup Test\n")
    
    all_passed = True
    
    # Test imports
    if not test_imports():
        all_passed = False
    
    # Test environment
    if not test_env_file():
        all_passed = False
    
    # Test streamlit
    if not test_streamlit():
        all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 All tests passed! You can now run the application with:")
        print("   streamlit run sap9.py")
    else:
        print("❌ Some tests failed. Please check the errors above and fix them.")
        print("\nSetup instructions:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Copy .env.example to .env")
        print("3. Add your OpenAI API key to the .env file")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
