# PDF Invoice Regex Generator Tool

This tool extracts structured data from PDF invoices and generates regex patterns for automated field extraction.

## Features

- 📄 **PDF Text Extraction**: Uses PyMuPDF to extract text from PDF invoices
- 🤖 **AI-Powered Field Extraction**: Uses OpenAI GPT to identify and extract key invoice fields
- 📝 **Smart File Renaming**: Automatically renames files based on extracted data
- 🔍 **Regex Pattern Generation**: Automatically generates regex patterns for field extraction
- ✅ **Pattern Verification**: Tests and refines regex patterns for accuracy
- 🌐 **Web Interface**: Easy-to-use Streamlit interface
- 🔗 **Regex101 Integration**: Direct links to test patterns on Regex101
- 📁 **File Management**: Track and manage renamed files with history

## Extracted Fields

- Invoice Number
- PO Number
- Date
- Subtotal
- Tax
- Document Total
- Delivery Fee
- Sale Line Items (with product details)

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Set Up OpenAI API Key

1. Copy the environment file template:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your OpenAI API key:
   ```
   OPENAI_API_KEY=your-actual-api-key-here
   ```

### 3. Run the Application

```bash
streamlit run sap9.py
```

The application will open in your default web browser at `http://localhost:8501`

## Usage

1. **Upload PDF Files**: Use the file uploader to select one or more PDF invoices
2. **Process Files**: Click "🚀 Run Full Extraction for All Files" to start processing
3. **Review Results**: The tool will show:
   - Extracted text from the PDF
   - Identified fields and values
   - Generated regex patterns
   - Pattern verification results
4. **Test Patterns**: Click the Regex101 links to test patterns online

## How It Works

1. **Text Extraction**: Extracts text from PDF while preserving layout
2. **Field Identification**: Uses GPT to identify and extract specific invoice fields
3. **Smart File Renaming**: Automatically renames files using format: `VENDOR_INVOICENUM_YYYYMMDD_[PO_PONUM].pdf`
4. **Data Formatting**: Converts extracted JSON data to clean plain text
5. **Pattern Generation**: Creates regex patterns based on the original text structure
6. **Verification**: Tests patterns against the extracted data and refines them

## Requirements

- Python 3.8+
- OpenAI API key
- Internet connection for API calls

## Troubleshooting

- **API Key Error**: Make sure your OpenAI API key is correctly set in the `.env` file
- **PDF Processing Issues**: Ensure PDFs are text-based (not scanned images)
- **Pattern Failures**: The tool automatically attempts to fix failed patterns

## File Outputs

The tool generates several files for each processed PDF:
- `{filename}_full_extraction.txt`: Raw extracted text
- `{filename}_extracted_fields.txt`: AI-extracted field data
- `{filename}_plaintext.txt`: Formatted field data
- `{filename}_layout_preserved.txt`: Relevant text snippets
- `{filename}_regex_patterns.txt`: Generated regex patterns
- `renamed_files/{new_filename}.pdf`: Renamed PDF file
- `file_rename_mapping.json`: Mapping of original to renamed files

## File Renaming Convention

Files are automatically renamed using this format:
```
VENDOR_INVOICENUM_YYYYMMDD_[PO_PONUM].pdf
```

**Examples:**
- `ABC_Company_INV-2024-001_20240315.pdf`
- `XYZ_Corp_12345_PO_67890_20240315.pdf`
- `ACME_Ltd_INV001_20240315.pdf`

**Components:**
- **VENDOR**: Company/vendor name (cleaned for filename safety)
- **INVOICENUM**: Invoice number from the document
- **YYYYMMDD**: Invoice date in ISO format
- **PO_PONUM**: Purchase order number (if different from invoice number)
