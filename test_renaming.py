#!/usr/bin/env python3
"""
Test script for the file renaming functionality
"""

import sys
import os
sys.path.append('.')

from sap9 import (
    clean_filename_component, 
    format_date_for_filename, 
    generate_new_filename,
    parse_extracted_fields
)

def test_clean_filename_component():
    """Test filename cleaning function"""
    print("🧪 Testing clean_filename_component...")
    
    test_cases = [
        ("ABC Company Ltd", "ABC_Company_Ltd"),
        ("Invoice #12345", "Invoice_12345"),
        ("Test/Company\\Name", "Test_Company_Name"),
        ("", "UNKNOWN"),
        ("Not Found", "UNKNOWN"),
        ("Very Long Company Name That Exceeds Fifty Characters Limit", "Very_Long_Company_Name_That_Exceeds_Fifty_Chara")
    ]
    
    for input_text, expected in test_cases:
        result = clean_filename_component(input_text)
        status = "✅" if result == expected else "❌"
        print(f"  {status} '{input_text}' -> '{result}' (expected: '{expected}')")

def test_format_date_for_filename():
    """Test date formatting function"""
    print("\n🧪 Testing format_date_for_filename...")
    
    test_cases = [
        ("15/03/2024", "20240315"),
        ("03/15/2024", "20240315"),
        ("2024-03-15", "20240315"),
        ("15-03-2024", "20240315"),
        ("March 15, 2024", "20240315"),
        ("Not Found", "20241"),  # Will use current date
        ("", "20241")  # Will use current date
    ]
    
    for input_date, expected_prefix in test_cases:
        result = format_date_for_filename(input_date)
        # For current date cases, just check if it's 8 digits
        if expected_prefix == "20241":
            status = "✅" if len(result) == 8 and result.startswith("2024") else "❌"
            print(f"  {status} '{input_date}' -> '{result}' (expected: current date)")
        else:
            status = "✅" if result == expected_prefix else "❌"
            print(f"  {status} '{input_date}' -> '{result}' (expected: '{expected_prefix}')")

def test_parse_extracted_fields():
    """Test field parsing function"""
    print("\n🧪 Testing parse_extracted_fields...")
    
    sample_content = """invoiceNumber:
INV-2024-001

poNumber:
PO-789456

date:
15/03/2024

subTotal:
$2,250.00

tax:
$227.50

documentTotal:
$2,502.50

deliveryFee:
$25.00"""
    
    result = parse_extracted_fields(sample_content)
    expected_fields = ['invoiceNumber', 'poNumber', 'date', 'subTotal', 'tax', 'documentTotal', 'deliveryFee']
    
    print(f"  Parsed fields: {list(result.keys())}")
    for field in expected_fields:
        if field in result:
            print(f"  ✅ {field}: '{result[field]}'")
        else:
            print(f"  ❌ {field}: Missing")

def test_generate_new_filename():
    """Test filename generation"""
    print("\n🧪 Testing generate_new_filename...")
    
    original_filename = "invoice_scan.pdf"
    vendor_name = "ABC_Company_Ltd"
    extracted_fields = {
        'invoiceNumber': 'INV-2024-001',
        'poNumber': 'PO-789456',
        'date': '15/03/2024'
    }
    
    result = generate_new_filename(original_filename, extracted_fields, vendor_name)
    print(f"  Original: '{original_filename}'")
    print(f"  Generated: '{result}'")
    
    # Check if it contains expected components
    expected_components = ['ABC_Company_Ltd', 'INV-2024-001', '20240315']
    all_present = all(comp in result for comp in expected_components)
    status = "✅" if all_present else "❌"
    print(f"  {status} Contains expected components: {expected_components}")

def main():
    """Run all tests"""
    print("🚀 File Renaming Functionality Tests\n")
    
    test_clean_filename_component()
    test_format_date_for_filename()
    test_parse_extracted_fields()
    test_generate_new_filename()
    
    print("\n🎯 Tests completed!")

if __name__ == "__main__":
    main()
