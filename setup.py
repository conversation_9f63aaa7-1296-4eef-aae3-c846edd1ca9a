#!/usr/bin/env python3
"""
Setup script for the PDF Invoice Regex Generator Tool
"""

import os
import sys
import subprocess

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def setup_env_file():
    """Set up the .env file"""
    print("\n🔧 Setting up environment file...")
    
    if os.path.exists('.env'):
        print("✅ .env file already exists")
        return True
    
    if not os.path.exists('.env.example'):
        print("❌ .env.example file not found")
        return False
    
    # Copy .env.example to .env
    try:
        with open('.env.example', 'r') as src:
            content = src.read()
        with open('.env', 'w') as dst:
            dst.write(content)
        print("✅ .env file created from template")
        print("⚠️  Please edit .env and add your OpenAI API key")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False

def create_sample_invoice():
    """Create a sample invoice for testing"""
    print("\n📄 Creating sample invoice...")
    
    if os.path.exists('sample_invoice.pdf'):
        print("✅ Sample invoice already exists")
        return True
    
    try:
        subprocess.check_call([sys.executable, "create_sample_invoice.py"])
        print("✅ Sample invoice created successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create sample invoice: {e}")
        return False

def test_setup():
    """Test the setup"""
    print("\n🧪 Testing setup...")
    
    try:
        result = subprocess.run([sys.executable, "test_setup.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Setup test passed")
            return True
        else:
            print("❌ Setup test failed:")
            print(result.stdout)
            return False
    except Exception as e:
        print(f"❌ Failed to run setup test: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 PDF Invoice Regex Generator Tool - Setup")
    print("=" * 50)
    
    success = True
    
    # Install dependencies
    if not install_dependencies():
        success = False
    
    # Setup environment file
    if not setup_env_file():
        success = False
    
    # Create sample invoice
    if not create_sample_invoice():
        success = False
    
    # Test setup (only if env file exists and has API key)
    if os.path.exists('.env'):
        from dotenv import load_dotenv
        load_dotenv()
        api_key = os.getenv('OPENAI_API_KEY')
        if api_key and api_key != 'your-openai-api-key-here':
            if not test_setup():
                success = False
        else:
            print("\n⚠️  Skipping setup test - OpenAI API key not configured")
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Edit .env file and add your OpenAI API key")
        print("2. Run the application with: streamlit run sap9.py")
        print("3. Or use the batch file: run_app.bat")
        print("4. Upload sample_invoice.pdf to test the tool")
    else:
        print("❌ Setup completed with errors. Please check the messages above.")
    
    return success

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
